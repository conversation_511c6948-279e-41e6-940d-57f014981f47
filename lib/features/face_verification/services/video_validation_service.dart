import 'dart:io';

import 'package:bloomg_flutter/features/face_verification/models/face_coverage_stats.dart';
import 'package:bloomg_flutter/features/face_verification/models/face_detection_result.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';

/// {@template video_validation_service}
/// Service that validates recorded videos for face verification quality.
/// Analyzes face coverage statistics and determines if the recording meets
/// requirements.
/// {@endtemplate}
class VideoValidationService {
  /// {@macro video_validation_service}
  VideoValidationService();

  final LoggerService _logger = LoggerService();

  // Enhanced validation thresholds for deep analysis quality
  static const double _minimumValidCoverageRate =
      80; // 80% of frames must have valid coverage for deep analysis
  static const double _minimumFaceDetectionRate =
      80; // 80% of frames must have face detected
  static const double _minimumAverageCoverage =
      80; // Average coverage must be ≥80% for quality analysis
  static const Duration _minimumRecordingDuration =
      Duration(seconds: 8); // At least 8 seconds
  static const Duration _maximumRecordingDuration =
      Duration(seconds: 10); // At most 10 seconds

  /// Validates a recorded video based on face detection results
  Future<VideoValidationResult> validateVideo({
    required String videoPath,
    required List<FaceDetectionResult> detectionResults,
    required Duration recordingDuration,
  }) async {
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Video validation started',
        'Path: $videoPath, Results: ${detectionResults.length}, '
            'Duration: ${recordingDuration.inSeconds}s',
      ),
    );

    try {
      // Check if video file exists
      final videoFile = File(videoPath);
      if (!await videoFile.exists()) {
        return VideoValidationResult.failure(
          reason: 'Video file not found',
          videoPath: videoPath,
        );
      }

      // Check file size
      final fileSize = await videoFile.length();
      if (fileSize == 0) {
        return VideoValidationResult.failure(
          reason: 'Video file is empty',
          videoPath: videoPath,
        );
      }

      // Calculate coverage statistics
      final stats =
          _calculateCoverageStats(detectionResults, recordingDuration);

      // Perform validation checks
      final validationChecks =
          _performValidationChecks(stats, recordingDuration);

      final result = VideoValidationResult(
        isValid: validationChecks.isValid,
        reason: validationChecks.reason,
        videoPath: videoPath,
        coverageStats: stats,
        validationChecks: validationChecks.checks,
        qualityScore: _calculateQualityScore(stats),
      );

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Video validation completed',
          'Valid: ${result.isValid}, '
              'Score: ${result.qualityScore.toStringAsFixed(1)}, '
              'Reason: ${result.reason}',
        ),
      );

      return result;
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.criticalError,
          'Video validation failed: $error',
        ),
        error,
        stackTrace,
      );

      return VideoValidationResult.failure(
        reason: 'Validation error: $error',
        videoPath: videoPath,
      );
    }
  }

  /// Calculates coverage statistics from detection results
  FaceCoverageStats _calculateCoverageStats(
    List<FaceDetectionResult> detectionResults,
    Duration recordingDuration,
  ) {
    if (detectionResults.isEmpty) {
      return const FaceCoverageStats.empty();
    }

    final totalFrames = detectionResults.length;
    final framesWithFace =
        detectionResults.where((result) => result.faceDetected).length;
    final framesWithValidCoverage =
        detectionResults.where((result) => result.meetsThreshold).length;

    final coverageValues =
        detectionResults.map((result) => result.coveragePercentage).toList();

    final averageCoverage = coverageValues.isNotEmpty
        ? coverageValues.reduce((a, b) => a + b) / coverageValues.length
        : 0.0;

    final minimumCoverage = coverageValues.isNotEmpty
        ? coverageValues.reduce((a, b) => a < b ? a : b)
        : 0.0;

    final maximumCoverage = coverageValues.isNotEmpty
        ? coverageValues.reduce((a, b) => a > b ? a : b)
        : 0.0;

    // Create initial stats without quality score
    final stats = FaceCoverageStats(
      totalFrames: totalFrames,
      framesWithFace: framesWithFace,
      framesWithValidCoverage: framesWithValidCoverage,
      averageCoverage: averageCoverage,
      minimumCoverage: minimumCoverage,
      maximumCoverage: maximumCoverage,
      recordingDuration: recordingDuration,
      detectionResults: List.from(detectionResults),
    );

    // Calculate quality score using the comprehensive algorithm
    final qualityScore = _calculateQualityScore(stats);

    // Return stats with quality score set
    return stats.copyWith(qualityScore: qualityScore);
  }

  /// Performs validation checks on the coverage statistics
  ValidationChecksResult _performValidationChecks(
    FaceCoverageStats stats,
    Duration recordingDuration,
  ) {
    final checks = <String, bool>{};
    final failures = <String>[];

    // Check recording duration
    checks['duration_minimum'] = recordingDuration >= _minimumRecordingDuration;
    if (!checks['duration_minimum']!) {
      failures.add(
        'Recording too short (${recordingDuration.inSeconds}s < '
        '${_minimumRecordingDuration.inSeconds}s)',
      );
    }

    checks['duration_maximum'] = recordingDuration <= _maximumRecordingDuration;
    if (!checks['duration_maximum']!) {
      failures.add(
        'Recording too long (${recordingDuration.inSeconds}s > '
        '${_maximumRecordingDuration.inSeconds}s)',
      );
    }

    // Check face detection rate
    checks['face_detection_rate'] =
        stats.faceDetectionRate >= _minimumFaceDetectionRate;
    if (!checks['face_detection_rate']!) {
      failures.add(
        'Insufficient face detection '
        '(${stats.faceDetectionRate.toStringAsFixed(1)}% < '
        '$_minimumFaceDetectionRate%)',
      );
    }

    // Check valid coverage rate
    checks['valid_coverage_rate'] =
        stats.validCoverageRate >= _minimumValidCoverageRate;
    if (!checks['valid_coverage_rate']!) {
      failures.add(
        'Insufficient valid coverage '
        '(${stats.validCoverageRate.toStringAsFixed(1)}% < '
        '$_minimumValidCoverageRate%)',
      );
    }

    // Check average coverage
    checks['average_coverage'] =
        stats.averageCoverage >= _minimumAverageCoverage;
    if (!checks['average_coverage']!) {
      failures.add(
        'Low average coverage '
        '(${stats.averageCoverage.toStringAsFixed(1)}% < '
        '$_minimumAverageCoverage%)',
      );
    }

    // Check minimum frame count
    checks['minimum_frames'] = stats.totalFrames >= 50; // At least 50 frames
    if (!checks['minimum_frames']!) {
      failures.add('Insufficient frames (${stats.totalFrames} < 50)');
    }

    final isValid = checks.values.every((check) => check);
    final reason = isValid
        ? 'Video validation passed all checks'
        : 'Validation failed: ${failures.join(', ')}';

    return ValidationChecksResult(
      isValid: isValid,
      reason: reason,
      checks: checks,
    );
  }

  /// Calculates coverage statistics with quality score for external use
  FaceCoverageStats calculateCoverageStatsWithQuality(
    List<FaceDetectionResult> detectionResults,
    Duration recordingDuration,
  ) {
    return _calculateCoverageStats(detectionResults, recordingDuration);
  }

  /// Calculates overall quality score (0-100)
  double _calculateQualityScore(FaceCoverageStats stats) {
    if (stats.totalFrames == 0) return 0;

    // Weight different factors
    const faceDetectionWeight = 0.25;
    const validCoverageWeight = 0.35;
    const averageCoverageWeight = 0.25;
    const consistencyWeight = 0.15;

    final faceDetectionScore = stats.faceDetectionRate;
    final validCoverageScore = stats.validCoverageRate;
    final averageCoverageScore = (stats.averageCoverage / 100) * 100;

    // Consistency score based on coverage variance
    final consistencyScore = _calculateConsistencyScore(stats);

    return (faceDetectionScore * faceDetectionWeight) +
        (validCoverageScore * validCoverageWeight) +
        (averageCoverageScore * averageCoverageWeight) +
        (consistencyScore * consistencyWeight);
  }

  /// Calculates consistency score based on coverage variance
  double _calculateConsistencyScore(FaceCoverageStats stats) {
    if (stats.detectionResults.length < 2) return 100;

    final coverageValues = stats.detectionResults
        .map((result) => result.coveragePercentage)
        .toList();

    // Calculate variance
    final mean = stats.averageCoverage;
    final variance = coverageValues
            .map((value) => (value - mean) * (value - mean))
            .reduce((a, b) => a + b) /
        coverageValues.length;

    final standardDeviation = variance.sqrt();

    // Convert to consistency score (lower variance = higher score)
    // Max standard deviation of 20 gives score of 0, SD of 0 gives score of 100
    final consistencyScore =
        ((20.0 - standardDeviation.clamp(0.0, 20.0)) / 20.0) * 100.0;

    return consistencyScore.clamp(0.0, 100.0);
  }
}

/// {@template video_validation_result}
/// Result of video validation containing validation status and details.
/// {@endtemplate}
class VideoValidationResult {
  /// {@macro video_validation_result}
  const VideoValidationResult({
    required this.isValid,
    required this.reason,
    required this.videoPath,
    this.coverageStats = const FaceCoverageStats.empty(),
    this.validationChecks = const {},
    this.qualityScore = 0.0,
  });

  /// Creates a failure result
  const VideoValidationResult.failure({
    required this.reason,
    required this.videoPath,
    this.coverageStats = const FaceCoverageStats.empty(),
    this.validationChecks = const {},
    this.qualityScore = 0.0,
  }) : isValid = false;

  /// Whether the video passed validation
  final bool isValid;

  /// Reason for validation result
  final String reason;

  /// Path to the validated video
  final String videoPath;

  /// Coverage statistics for the video
  final FaceCoverageStats coverageStats;

  /// Individual validation check results
  final Map<String, bool> validationChecks;

  /// Overall quality score (0-100)
  final double qualityScore;

  @override
  String toString() {
    return 'VideoValidationResult('
        'isValid: $isValid, '
        'qualityScore: ${qualityScore.toStringAsFixed(1)}, '
        'reason: $reason'
        ')';
  }
}

/// {@template validation_checks_result}
/// Result of individual validation checks.
/// {@endtemplate}
class ValidationChecksResult {
  /// {@macro validation_checks_result}
  const ValidationChecksResult({
    required this.isValid,
    required this.reason,
    required this.checks,
  });

  /// Whether all checks passed
  final bool isValid;

  /// Summary reason
  final String reason;

  /// Individual check results
  final Map<String, bool> checks;
}

/// Extension to add sqrt method to double
extension DoubleExtension on double {
  /// Square root of the number
  double sqrt() => this < 0
      ? double.nan
      : this == 0
          ? 0
          : toDouble().squareRoot();

  /// Helper method for square root calculation
  double squareRoot() {
    if (this < 0) return double.nan;
    if (this == 0) return 0;

    var x = this;
    double prev = 0;

    while ((x - prev).abs() > 0.0001) {
      prev = x;
      x = (x + this / x) / 2;
    }

    return x;
  }
}
