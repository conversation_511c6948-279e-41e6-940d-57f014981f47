import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:bloomg_flutter/features/face_verification/models/face_coverage_stats.dart';
import 'package:bloomg_flutter/features/face_verification/models/face_detection_result.dart';
import 'package:bloomg_flutter/features/face_verification/models/video_capture_config.dart';
import 'package:bloomg_flutter/features/face_verification/repository/face_detection_repository.dart';
import 'package:bloomg_flutter/features/face_verification/repository/video_storage_repository.dart';
import 'package:bloomg_flutter/features/face_verification/services/video_validation_service.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:equatable/equatable.dart';

part 'face_video_capture_event.dart';
part 'face_video_capture_state.dart';

/// {@template face_video_capture_bloc}
/// BLoC that manages the face verification video capture process.
///
/// Handles camera initialization, countdown, recording, face detection,
/// and video validation according to the specified requirements.
/// {@endtemplate}
class FaceVideoCaptureBloc
    extends Bloc<FaceVideoCaptureEvent, FaceVideoCaptureState> {
  /// {@macro face_video_capture_bloc}
  FaceVideoCaptureBloc({
    required FaceDetectionRepository faceDetectionRepository,
    required VideoStorageRepository videoStorageRepository,
    required VideoValidationService videoValidationService,
    VideoCaptureConfig? config,
  })  : _faceDetectionRepository = faceDetectionRepository,
        _videoStorageRepository = videoStorageRepository,
        _videoValidationService = videoValidationService,
        _config = config ?? const VideoCaptureConfig(),
        super(const Initial()) {
    // Register event handlers
    on<InitializeCamera>(_onInitializeCamera);
    on<StartCountdown>(_onStartCountdown);
    on<CountdownTick>(_onCountdownTick);
    on<StartRecording>(_onStartRecording);
    on<StopRecording>(_onStopRecording);
    on<ProcessFrame>(_onProcessFrame);
    on<RecordingProgress>(_onRecordingProgress);
    on<FaceDetectionStatusChanged>(_onFaceDetectionStatusChanged);
    on<AbortCountdown>(_onAbortCountdown);
    on<ResetCapture>(_onResetCapture);
    on<DisposeResources>(_onDisposeResources);

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'BLoC initialized',
        'Config: $_config',
      ),
    );
  }

  final FaceDetectionRepository _faceDetectionRepository;
  final VideoStorageRepository _videoStorageRepository;
  final VideoValidationService _videoValidationService;
  final VideoCaptureConfig _config;
  final LoggerService _logger = LoggerService();

  // Timers for countdown and recording
  Timer? _countdownTimer;
  Timer? _recordingTimer;
  Timer? _faceDetectionTimer;

  // Recording state tracking
  DateTime? _recordingStartTime;
  final List<FaceDetectionResult> _detectionResults = [];

  // Enhanced frame smoothing for stability
  final List<FaceDetectionResult> _frameBuffer = [];
  static const int _frameBufferSize =
      8; // Increased from 5 for better stability
  FaceDetectionResult? _smoothedDetection;

  // Hysteresis thresholds for stable detection
  static const double _detectionGainThreshold =
      80; // Revised threshold to gain detection for better quality
  static const double _detectionLossThreshold =
      75; // Revised threshold to lose detection for stability

  // State update debouncing
  DateTime? _lastStateUpdate;
  static const Duration _minStateUpdateInterval = Duration(milliseconds: 200);

  // Detection state tracking
  bool _currentDetectionState = false;

  @override
  Future<void> close() async {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'BLoC closing',
        'Disposing timers and resources',
      ),
    );

    // Cancel all timers
    _countdownTimer?.cancel();
    _recordingTimer?.cancel();
    _faceDetectionTimer?.cancel();

    // Dispose repositories
    await _faceDetectionRepository.dispose();
    await _videoStorageRepository.dispose();

    return super.close();
  }

  /// Handles camera initialization
  Future<void> _onInitializeCamera(
    InitializeCamera event,
    Emitter<FaceVideoCaptureState> emit,
  ) async {
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Camera initialization started',
      ),
    );

    emit(CameraInitializing(config: _config));

    try {
      // Initialize face detection
      await _faceDetectionRepository.initialize();

      // Initialize video storage
      await _videoStorageRepository.initialize();

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Camera initialization completed',
        ),
      );

      // Start face detection immediately for recording readiness monitoring
      _startFaceDetectionForReadiness();

      emit(CameraReady(config: _config));
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.criticalError,
          'Camera initialization failed: $error',
        ),
        error,
        stackTrace,
      );

      emit(
        Error(error: 'Failed to initialize camera: $error', config: _config),
      );
    }
  }

  /// Handles countdown start
  Future<void> _onStartCountdown(
    StartCountdown event,
    Emitter<FaceVideoCaptureState> emit,
  ) async {
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Countdown started',
        'Duration: ${_config.countdownDuration.inSeconds}s',
      ),
    );

    var remainingSeconds = _config.countdownDuration.inSeconds;
    emit(
      CountdownInProgress(
        remainingSeconds: remainingSeconds,
        config: _config,
        currentDetection: state.currentDetection,
        coverageStats: state.coverageStats,
      ),
    );

    _countdownTimer = Timer.periodic(
      const Duration(seconds: 1),
      (timer) {
        remainingSeconds--;
        if (remainingSeconds > 0) {
          add(CountdownTick(remainingSeconds));
        } else {
          timer.cancel();
          add(const StartRecording());
        }
      },
    );
  }

  /// Handles countdown tick
  void _onCountdownTick(
    CountdownTick event,
    Emitter<FaceVideoCaptureState> emit,
  ) {
    emit(
      CountdownInProgress(
        remainingSeconds: event.remainingSeconds,
        config: _config,
        currentDetection: state.currentDetection,
        coverageStats: state.coverageStats,
      ),
    );
  }

  /// Handles recording start
  Future<void> _onStartRecording(
    StartRecording event,
    Emitter<FaceVideoCaptureState> emit,
  ) async {
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Recording started',
        'Duration: ${_config.recordingDuration.inSeconds}s',
      ),
    );

    try {
      // Start video recording
      await _videoStorageRepository.startRecording();

      _recordingStartTime = DateTime.now();
      _detectionResults.clear();
      _frameBuffer.clear();
      _smoothedDetection = null;

      // Reset detection state for fresh recording
      _currentDetectionState = false;
      _lastStateUpdate = null;

      // Start face detection timer
      _startFaceDetection();

      // Start recording progress timer
      _startRecordingTimer();

      emit(
        Recording(
          elapsedTime: Duration.zero,
          remainingTime: _config.recordingDuration,
          config: _config,
          currentDetection: state.currentDetection,
          coverageStats: state.coverageStats,
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.criticalError,
          'Failed to start recording: $error',
        ),
        error,
        stackTrace,
      );

      emit(Error(error: 'Failed to start recording: $error', config: _config));
    }
  }

  /// Starts face detection for recording readiness monitoring
  void _startFaceDetectionForReadiness() {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Face detection for recording readiness started',
        'Monitoring face coverage for 70% threshold',
      ),
    );
    // Face detection is handled by camera preview widget
    // This method is for documentation purposes
  }

  /// Starts the face detection timer
  ///
  /// Note: Face detection is now handled directly by the camera preview widget
  /// through real-time frame analysis. This method is kept for compatibility
  /// but does not start a timer.
  void _startFaceDetection() {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Face detection handled by camera preview widget',
        'Timer-based detection disabled',
      ),
    );
    // Face detection is now handled by camera preview widget
    // No timer needed as frames are processed in real-time
  }

  /// Starts the recording progress timer
  void _startRecordingTimer() {
    _recordingTimer = Timer.periodic(
      const Duration(milliseconds: 100),
      (timer) {
        if (_recordingStartTime == null) return;

        final elapsed = DateTime.now().difference(_recordingStartTime!);
        final remaining = _config.recordingDuration - elapsed;

        if (remaining.inMilliseconds <= 0) {
          timer.cancel();
          add(const StopRecording());
        } else {
          add(RecordingProgress(elapsed, remaining));
        }
      },
    );
  }

  /// Handles frame processing for face detection
  void _onProcessFrame(
    ProcessFrame event,
    Emitter<FaceVideoCaptureState> emit,
  ) {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'ProcessFrame event received',
        'Faces: ${event.detectionResult.faceCount}, '
            'Coverage: '
            '${event.detectionResult.coveragePercentage.toStringAsFixed(1)}%, '
            'Valid: ${event.detectionResult.isValidDetection}',
      ),
    );

    // Add to frame buffer for smoothing
    _addToFrameBuffer(event.detectionResult);

    // Calculate smoothed detection result
    final smoothedResult = _calculateSmoothedDetection();

    _detectionResults.add(event.detectionResult);

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Detection result processed',
        'Raw: ${event.detectionResult.coveragePercentage.toStringAsFixed(1)}%, '
            'Smooth: '
            '${smoothedResult?.coveragePercentage.toStringAsFixed(1) ?? 'N/A'}'
            '%',
      ),
    );

    // Check for face detection status changes for recording readiness
    if (smoothedResult != null &&
        (state is CameraReady || state is CountdownInProgress)) {
      final canStartRecording =
          _canStartRecordingBasedOnDetection(smoothedResult);

      // Trigger face detection status change event
      add(
        FaceDetectionStatusChanged(
          canStartRecording: canStartRecording,
          detectionResult: smoothedResult,
        ),
      );
    }

    // Update current state with smoothed detection only if recording
    // and only if detection status has meaningfully changed
    if (state is Recording && smoothedResult != null) {
      final recordingState = state as Recording;
      final previousDetection = _smoothedDetection;

      // Only emit new state if smoothed detection changed significantly
      final shouldUpdate = _shouldUpdateDetectionState(
        previousDetection,
        smoothedResult,
      );

      if (shouldUpdate) {
        _smoothedDetection = smoothedResult;

        final prevCoverage =
            previousDetection?.coveragePercentage.toStringAsFixed(1) ?? 'N/A';
        final currCoverage =
            smoothedResult.coveragePercentage.toStringAsFixed(1);
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Recording state updated with smoothed detection',
            '$prevCoverage% -> $currCoverage%',
          ),
        );

        emit(
          Recording(
            elapsedTime: recordingState.elapsedTime,
            remainingTime: recordingState.remainingTime,
            config: _config,
            currentDetection: smoothedResult,
            coverageStats: _calculateCoverageStats(),
          ),
        );
      } else {
        final coverageDiff = (previousDetection?.coveragePercentage ??
                0 - smoothedResult.coveragePercentage)
            .abs();
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'State update skipped - no significant smoothed change',
            'Coverage diff: ${coverageDiff.toStringAsFixed(1)}%',
          ),
        );
      }
    } else {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'ProcessFrame event ignored',
          'Recording: ${state is Recording}, '
              'Smoothed result: ${smoothedResult != null}',
        ),
      );
    }
  }

  /// Determines if detection state should trigger UI update
  bool _shouldUpdateDetectionState(
    FaceDetectionResult? previous,
    FaceDetectionResult current,
  ) {
    // Always update if no previous detection
    if (previous == null) return true;

    // Update if face detection status changed
    if (previous.faceDetected != current.faceDetected) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detection status changed',
          'Previous: ${previous.faceDetected}, '
              'Current: ${current.faceDetected}',
        ),
      );
      return true;
    }

    // Update if face count changed
    if (previous.faceCount != current.faceCount) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face count changed',
          'Previous: ${previous.faceCount}, Current: ${current.faceCount}',
        ),
      );
      return true;
    }

    // Update if coverage changed by more than 8% (increased for stability)
    final coverageDiff =
        (previous.coveragePercentage - current.coveragePercentage).abs();
    if (coverageDiff > 8.0) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Coverage changed significantly',
          'Previous: ${previous.coveragePercentage.toStringAsFixed(1)}%, '
              'Current: ${current.coveragePercentage.toStringAsFixed(1)}%, '
              'Diff: ${coverageDiff.toStringAsFixed(1)}%',
        ),
      );
      return true;
    }

    // Don't update for minor changes
    return false;
  }

  /// Handles recording progress updates
  void _onRecordingProgress(
    RecordingProgress event,
    Emitter<FaceVideoCaptureState> emit,
  ) {
    if (state is Recording) {
      emit(
        Recording(
          elapsedTime: event.elapsedTime,
          remainingTime: event.remainingTime,
          config: _config,
          currentDetection: state.currentDetection,
          coverageStats: _calculateCoverageStats(),
        ),
      );
    }
  }

  /// Handles face detection status changes for recording readiness
  /// Implements debouncing to prevent rapid UI updates
  void _onFaceDetectionStatusChanged(
    FaceDetectionStatusChanged event,
    Emitter<FaceVideoCaptureState> emit,
  ) {
    // Implement state update debouncing
    final now = DateTime.now();
    if (_lastStateUpdate != null &&
        now.difference(_lastStateUpdate!) < _minStateUpdateInterval) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'State update debounced',
          'Time since last update: '
              '${now.difference(_lastStateUpdate!).inMilliseconds}ms',
        ),
      );
      return; // Skip this update to prevent rapid state changes
    }

    _lastStateUpdate = now;

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Face detection status changed',
        'Can start recording: ${event.canStartRecording}, '
            'Coverage: '
            '${event.detectionResult.coveragePercentage.toStringAsFixed(1)}%',
      ),
    );

    // Update state based on current state type
    if (state is CameraReady) {
      emit(
        CameraReady(
          canStartRecording: event.canStartRecording,
          config: _config,
          currentDetection: event.detectionResult,
          coverageStats: state.coverageStats,
        ),
      );
    } else if (state is CountdownInProgress) {
      final countdownState = state as CountdownInProgress;

      // Check if face is lost during countdown
      if (!event.canStartRecording) {
        _logger.warning(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Face lost during countdown',
            'Aborting countdown at ${countdownState.remainingSeconds}s '
                'remaining',
          ),
        );

        add(
          const AbortCountdown(
            reason: 'Face detection lost during countdown. '
                'Please reposition your face.',
          ),
        );
        return;
      }

      // Update countdown state with face validation status
      final validationStatus = event.canStartRecording
          ? 'Face detected - countdown continuing...'
          : 'Position your face in the guide area';

      emit(
        CountdownInProgress(
          remainingSeconds: countdownState.remainingSeconds,
          faceValidationStatus: validationStatus,
          config: _config,
          currentDetection: event.detectionResult,
          coverageStats: state.coverageStats,
        ),
      );
    }
  }

  /// Handles countdown abort when face detection requirements are not met
  void _onAbortCountdown(
    AbortCountdown event,
    Emitter<FaceVideoCaptureState> emit,
  ) {
    _logger.warning(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Countdown aborted',
        'Reason: ${event.reason}',
      ),
    );

    // Cancel countdown timer
    _countdownTimer?.cancel();

    // Return to camera ready state with error message
    emit(
      CameraReady(
        config: _config,
        currentDetection: state.currentDetection,
        coverageStats: state.coverageStats,
        errorMessage: event.reason,
      ),
    );
  }

  /// Handles recording stop and validation
  Future<void> _onStopRecording(
    StopRecording event,
    Emitter<FaceVideoCaptureState> emit,
  ) async {
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Recording stopped',
        'Processing ${_detectionResults.length} detection results',
      ),
    );

    // Cancel timers
    _recordingTimer?.cancel();
    _faceDetectionTimer?.cancel();

    emit(
      Processing(
        config: _config,
        currentDetection: state.currentDetection,
        coverageStats: _calculateCoverageStats(),
      ),
    );

    try {
      // Stop video recording and get file path
      final videoPath = await _videoStorageRepository.stopRecording();

      // Calculate final coverage statistics
      final finalStats = _calculateCoverageStats();

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Recording validation completed',
          'Quality score: ${finalStats.qualityScore.toStringAsFixed(1)}, '
              'Meets threshold: ${finalStats.meetsQualityThreshold}',
        ),
      );

      // Determine success or failure based on quality threshold
      if (finalStats.meetsQualityThreshold) {
        emit(
          Success(
            videoPath: videoPath,
            config: _config,
            currentDetection: state.currentDetection,
            coverageStats: finalStats,
          ),
        );
      } else {
        emit(
          Failure(
            reason: 'Insufficient face coverage. '
                'Quality score: ${finalStats.qualityScore.toStringAsFixed(1)}% '
                '(minimum required: 70%)',
            videoPath: videoPath,
            config: _config,
            currentDetection: state.currentDetection,
            coverageStats: finalStats,
          ),
        );
      }
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.criticalError,
          'Recording validation failed: $error',
        ),
        error,
        stackTrace,
      );

      emit(
        Error(error: 'Failed to process recording: $error', config: _config),
      );
    }
  }

  /// Handles capture reset
  void _onResetCapture(
    ResetCapture event,
    Emitter<FaceVideoCaptureState> emit,
  ) {
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Capture reset',
      ),
    );

    // Cancel all timers
    _countdownTimer?.cancel();
    _recordingTimer?.cancel();
    _faceDetectionTimer?.cancel();

    // Clear detection results and frame buffer
    _detectionResults.clear();
    _frameBuffer.clear();
    _smoothedDetection = null;
    _recordingStartTime = null;

    // Reset detection state and debouncing
    _currentDetectionState = false;
    _lastStateUpdate = null;

    emit(CameraReady(config: _config));
  }

  /// Handles resource disposal
  Future<void> _onDisposeResources(
    DisposeResources event,
    Emitter<FaceVideoCaptureState> emit,
  ) async {
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Resources disposal started',
      ),
    );

    // Cancel all timers
    _countdownTimer?.cancel();
    _recordingTimer?.cancel();
    _faceDetectionTimer?.cancel();

    try {
      // Dispose repositories
      await _faceDetectionRepository.dispose();
      await _videoStorageRepository.dispose();

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Resources disposal completed',
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.recoverableError,
          'Resource disposal error: $error',
        ),
        error,
        stackTrace,
      );
    }

    emit(const Initial());
  }

  /// Calculates coverage statistics from detection results
  FaceCoverageStats _calculateCoverageStats() {
    if (_detectionResults.isEmpty) {
      return const FaceCoverageStats.empty();
    }

    final recordingDuration = _recordingStartTime != null
        ? DateTime.now().difference(_recordingStartTime!)
        : Duration.zero;

    // Use VideoValidationService to calculate stats with quality score
    return _videoValidationService.calculateCoverageStatsWithQuality(
      _detectionResults,
      recordingDuration,
    );
  }

  /// Adds a detection result to the frame buffer for smoothing
  void _addToFrameBuffer(FaceDetectionResult result) {
    _frameBuffer.add(result);

    // Keep buffer size limited
    if (_frameBuffer.length > _frameBufferSize) {
      _frameBuffer.removeAt(0);
    }

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Frame added to smoothing buffer',
        'Buffer size: ${_frameBuffer.length}/$_frameBufferSize, '
            'Face detected: ${result.faceDetected}, '
            'Coverage: ${result.coveragePercentage.toStringAsFixed(1)}%',
      ),
    );
  }

  /// Calculates smoothed detection result using enhanced weighted averaging
  FaceDetectionResult? _calculateSmoothedDetection() {
    if (_frameBuffer.isEmpty) return null;

    // Use majority voting for face detection status
    final faceDetectedCount =
        _frameBuffer.where((result) => result.faceDetected).length;

    final majorityFaceDetected =
        faceDetectedCount >= (_frameBufferSize / 2).ceil();

    if (!majorityFaceDetected) {
      // If majority says no face, return a "no face" result
      return FaceDetectionResult(
        faceDetected: false,
        faceCount: 0,
        coveragePercentage: 0,
        timestamp: DateTime.now(),
      );
    }

    // For face detected cases, use weighted averaging
    // (recent frames have more weight)
    final validDetections =
        _frameBuffer.where((result) => result.faceDetected).toList();

    if (validDetections.isEmpty) {
      return FaceDetectionResult(
        faceDetected: false,
        faceCount: 0,
        coveragePercentage: 0,
        timestamp: DateTime.now(),
      );
    }

    // Calculate weighted average coverage (recent frames have more influence)
    double weightedCoverageSum = 0;
    double totalWeight = 0;

    for (var i = 0; i < validDetections.length; i++) {
      // Weight increases linearly for more recent frames
      final weight = (i + 1).toDouble();
      weightedCoverageSum += validDetections[i].coveragePercentage * weight;
      totalWeight += weight;
    }

    final weightedAverageCoverage =
        totalWeight > 0 ? weightedCoverageSum / totalWeight : 0.0;

    final averageFaceCount = (validDetections
                .map((result) => result.faceCount)
                .reduce((a, b) => a + b) /
            validDetections.length)
        .round();

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Enhanced smoothed detection calculated',
        'Face detected votes: $faceDetectedCount/${_frameBuffer.length}, '
            'Majority detected: $majorityFaceDetected, '
            'Weighted coverage: '
            '${weightedAverageCoverage.toStringAsFixed(1)}%, '
            'Valid detections: ${validDetections.length}',
      ),
    );

    return FaceDetectionResult(
      faceDetected: true,
      faceCount: averageFaceCount,
      coveragePercentage: weightedAverageCoverage,
      timestamp: DateTime.now(),
    );
  }

  /// Determines if recording can be started based on face detection result
  /// Uses hysteresis to prevent rapid state changes
  bool _canStartRecordingBasedOnDetection(FaceDetectionResult detection) {
    // Implement hysteresis for stable detection
    final currentCoverage = detection.coveragePercentage;
    final hasValidFace = detection.faceDetected && detection.faceCount == 1;

    bool newDetectionState;

    if (_currentDetectionState) {
      // Currently detecting - use lower threshold to lose detection
      newDetectionState =
          hasValidFace && currentCoverage >= _detectionLossThreshold;
    } else {
      // Not currently detecting - use higher threshold to gain detection
      newDetectionState =
          hasValidFace && currentCoverage >= _detectionGainThreshold;
    }

    // Update detection state
    final stateChanged = newDetectionState != _currentDetectionState;
    _currentDetectionState = newDetectionState;

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Recording readiness check with hysteresis',
        'Face detected: ${detection.faceDetected}, '
            'Face count: ${detection.faceCount}, '
            'Coverage: ${currentCoverage.toStringAsFixed(1)}%, '
            'Previous state: ${!newDetectionState}, '
            'New state: $newDetectionState, '
            'State changed: $stateChanged',
      ),
    );

    return newDetectionState;
  }
}
