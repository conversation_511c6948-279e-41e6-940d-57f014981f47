import 'package:bloomg_flutter/core/di/injection.dart';
import 'package:bloomg_flutter/core/router/app_router.dart';
import 'package:bloomg_flutter/features/face_verification/bloc/face_video_capture_bloc.dart';
import 'package:bloomg_flutter/features/face_verification/view/widgets/camera_preview_widget.dart';
import 'package:bloomg_flutter/features/face_verification/view/widgets/countdown_timer_widget.dart';
import 'package:bloomg_flutter/features/face_verification/view/widgets/recording_feedback_widget.dart';
import 'package:bloomg_flutter/features/face_verification/view/widgets/result_screen_widget.dart';
import 'package:bloomg_flutter/features/video_gallery/services/video_persistence_service.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:responsive_framework/responsive_framework.dart';

/// {@template face_video_capture_page}
/// Main page for face verification video capture.
///
/// Provides a full-screen camera interface with face detection guidance,
/// countdown timer, recording feedback, and result screens.
/// {@endtemplate}
class FaceVideoCapturePage extends StatefulWidget {
  /// {@macro face_video_capture_page}
  const FaceVideoCapturePage({super.key});

  @override
  State<FaceVideoCapturePage> createState() => _FaceVideoCapturePageState();
}

class _FaceVideoCapturePageState extends State<FaceVideoCapturePage>
    with WidgetsBindingObserver {
  final LoggerService _logger = LoggerService();
  final VideoPersistenceService _videoPersistenceService =
      VideoPersistenceService();

  /// Direct reference to the BLoC instance for safe access during
  /// lifecycle events
  FaceVideoCaptureBloc? _bloc;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Face video capture page initialized',
      ),
    );
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Face video capture page disposed',
      ),
    );

    // Properly dispose resources before widget disposal
    _disposeResources();

    super.dispose();
  }

  /// Handles proper resource disposal before navigation or widget disposal
  Future<void> _disposeResources() async {
    try {
      // First dispose BLoC resources
      if (_bloc != null && !_bloc!.isClosed) {
        _bloc!.add(const DisposeResources());
        await _bloc!.close();
      }
      _bloc = null;

      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Resources disposed successfully',
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.recoverableError,
          'Resource disposal error: $error',
        ),
        error,
        stackTrace,
      );
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // Handle app lifecycle changes for camera management
    switch (state) {
      case AppLifecycleState.paused:
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'App paused - disposing camera resources',
          ),
        );
        _safelyAccessBloc((bloc) => bloc.add(const DisposeResources()));
      case AppLifecycleState.resumed:
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'App resumed - reinitializing camera',
          ),
        );
        _safelyAccessBloc((bloc) => bloc.add(const InitializeCamera()));
      case AppLifecycleState.inactive:
      case AppLifecycleState.detached:
      case AppLifecycleState.hidden:
        // No action needed for these states
        break;
    }
  }

  /// Safely access the BLoC with proper error handling
  void _safelyAccessBloc(void Function(FaceVideoCaptureBloc) action) {
    try {
      // First try to use the stored reference
      if (_bloc != null && !_bloc!.isClosed) {
        action(_bloc!);
        return;
      }

      // Fallback to context.read if mounted and context is available
      if (mounted) {
        final bloc = context.read<FaceVideoCaptureBloc>();
        action(bloc);
        return;
      }

      _logger.warning(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'BLoC access skipped - widget not mounted or BLoC unavailable',
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.recoverableError,
          'Failed to access FaceVideoCaptureBloc during lifecycle change: '
          '$error',
        ),
        error,
        stackTrace,
      );
    }
  }

  /// Handles back navigation with proper resource cleanup
  Future<void> _handleBackNavigation() async {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Back navigation initiated - disposing resources',
      ),
    );

    // Dispose resources before navigation
    await _disposeResources();

    // Navigate back only if still mounted
    if (mounted) {
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) {
        // Create and store the BLoC reference for safe lifecycle access
        _bloc = getIt<FaceVideoCaptureBloc>();
        _bloc!.add(const InitializeCamera());
        return _bloc!;
      },
      child: ResponsiveBreakpoints.builder(
        child: Scaffold(
          backgroundColor: Colors.black,
          body: SafeArea(
            child: BlocConsumer<FaceVideoCaptureBloc, FaceVideoCaptureState>(
              listener: _handleStateChanges,
              builder: _buildContent,
            ),
          ),
        ),
        breakpoints: const [
          Breakpoint(start: 0, end: 450, name: MOBILE),
          Breakpoint(start: 451, end: 800, name: TABLET),
          Breakpoint(start: 801, end: 1920, name: DESKTOP),
          Breakpoint(start: 1921, end: double.infinity, name: '4K'),
        ],
      ),
    );
  }

  /// Handles state changes and shows appropriate feedback
  void _handleStateChanges(BuildContext context, FaceVideoCaptureState state) {
    switch (state.runtimeType) {
      case Error:
        final errorState = state as Error;
        _logger.error(
          LoggingConstants.formatError(
            LoggingConstants.faceVerificationModule,
            LoggingConstants.criticalError,
            'Face capture error: ${errorState.error}',
          ),
        );

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${errorState.error}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'Retry',
              textColor: Colors.white,
              onPressed: () {
                _safelyAccessBloc((bloc) => bloc.add(const ResetCapture()));
              },
            ),
          ),
        );

      case Success:
        final successState = state as Success;
        _logger.info(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Face verification completed successfully',
            'Quality score: '
                '${successState.coverageStats.qualityScore.toStringAsFixed(1)}',
          ),
        );

      case Failure:
        final failureState = state as Failure;
        _logger.warning(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Face verification failed',
            'Reason: ${failureState.reason}',
          ),
        );

      default:
        break;
    }
  }

  /// Builds the main content based on current state
  Widget _buildContent(BuildContext context, FaceVideoCaptureState state) {
    switch (state.runtimeType) {
      case Initial:
      case CameraInitializing:
        return _buildLoadingScreen();

      case CameraReady:
        return _buildCameraReadyScreen(context, state);

      case CountdownInProgress:
        final countdownState = state as CountdownInProgress;
        return _buildCountdownScreen(context, countdownState);

      case Recording:
        final recordingState = state as Recording;
        return _buildRecordingScreen(context, recordingState);

      case Processing:
        return _buildProcessingScreen();

      case Success:
        final successState = state as Success;
        return _buildSuccessScreen(context, successState);

      case Failure:
        final failureState = state as Failure;
        return _buildFailureScreen(context, failureState);

      case Error:
        final errorState = state as Error;
        return _buildErrorScreen(context, errorState);

      default:
        return _buildLoadingScreen();
    }
  }

  /// Builds loading screen
  Widget _buildLoadingScreen() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
          SizedBox(height: 16),
          Text(
            'Initializing camera...',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  /// Builds camera ready screen
  Widget _buildCameraReadyScreen(
    BuildContext context,
    FaceVideoCaptureState state,
  ) {
    final cameraReadyState = state as CameraReady;

    return Stack(
      children: [
        // Camera preview
        const CameraPreviewWidget(),

        // Instructions and start button
        Positioned(
          bottom: 100,
          left: 20,
          right: 20,
          child: Column(
            children: [
              // Error message if any
              if (state.errorMessage != null) ...[
                Container(
                  padding: const EdgeInsets.all(16),
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color: Colors.red.withValues(alpha: 0.8),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    state.errorMessage!,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],

              // Instructions
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  _getInstructionText(cameraReadyState),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 20),

              // Recording button with conditional state
              ElevatedButton(
                onPressed: cameraReadyState.canStartRecording
                    ? () {
                        _safelyAccessBloc(
                          (bloc) => bloc.add(const StartCountdown()),
                        );
                      }
                    : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: cameraReadyState.canStartRecording
                      ? Colors.blue
                      : Colors.grey,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 32,
                    vertical: 16,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(25),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (!cameraReadyState.canStartRecording) ...[
                      const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                    ],
                    Text(
                      cameraReadyState.canStartRecording
                          ? 'Start Recording'
                          : 'Detecting Face...',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        // Back button
        Positioned(
          top: 20,
          left: 20,
          child: IconButton(
            onPressed: _handleBackNavigation,
            icon: const Icon(
              Icons.arrow_back,
              color: Colors.white,
              size: 28,
            ),
          ),
        ),
      ],
    );
  }

  /// Gets instruction text based on camera ready state
  String _getInstructionText(CameraReady state) {
    if (state.canStartRecording) {
      return 'Perfect! Your face is detected. Tap Start Recording to begin.';
    } else {
      final coverage = state.currentDetection?.coveragePercentage ?? 0;
      if (state.currentDetection?.faceDetected ?? false) {
        if (state.currentDetection!.faceCount > 1) {
          return 'Multiple faces detected. '
              'Please ensure only one person is visible.';
        } else if (coverage < 85) {
          return 'Move closer to the camera for deep analysis quality. '
              'Face coverage: ${coverage.toStringAsFixed(0)}% (need 80%)';
        }
      }
      return 'Position your face closer in the center guide for optimal '
          'quality. Ensure good lighting and face the camera directly.';
    }
  }

  /// Builds countdown screen
  Widget _buildCountdownScreen(
    BuildContext context,
    CountdownInProgress state,
  ) {
    return Stack(
      children: [
        // Camera preview
        const CameraPreviewWidget(),

        // Countdown timer
        Center(
          child: CountdownTimerWidget(
            remainingSeconds: state.remainingSeconds,
          ),
        ),

        // Face validation status
        Positioned(
          bottom: 100,
          left: 20,
          right: 20,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.8),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              state.faceValidationStatus,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ],
    );
  }

  /// Builds recording screen
  Widget _buildRecordingScreen(
    BuildContext context,
    Recording state,
  ) {
    return Stack(
      children: [
        // Camera preview
        const CameraPreviewWidget(),

        // Recording feedback
        RecordingFeedbackWidget(
          elapsedTime: state.elapsedTime,
          remainingTime: state.remainingTime,
          progress: state.progress,
          currentDetection: state.currentDetection,
        ),
      ],
    );
  }

  /// Builds processing screen
  Widget _buildProcessingScreen() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
          SizedBox(height: 16),
          Text(
            'Processing video...',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  /// Builds success screen
  Widget _buildSuccessScreen(BuildContext context, Success state) {
    return ResultScreenWidget(
      isSuccess: true,
      title: 'Verification Successful!',
      message: 'Your face verification video has been recorded successfully.',
      coverageStats: state.coverageStats,
      onRetry: () {
        _safelyAccessBloc((bloc) => bloc.add(const ResetCapture()));
      },
      onContinue: () => _handleSuccessfulVerification(context, state),
    );
  }

  /// Handles successful verification by saving video and navigating to gallery
  Future<void> _handleSuccessfulVerification(
    BuildContext context,
    Success state,
  ) async {
    // Store context references before async operations
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final goRouter = GoRouter.of(context);

    try {
      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Processing successful verification',
          'Video path: ${state.videoPath}',
        ),
      );

      // Save the video to permanent storage
      final savedVideoPath = await _videoPersistenceService.saveSuccessfulVideo(
        temporaryVideoPath: state.videoPath,
        coverageStats: state.coverageStats,
        recordingTime: DateTime.now(),
      );

      if (savedVideoPath != null) {
        _logger.info(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Video saved successfully',
            'Saved path: $savedVideoPath',
          ),
        );

        // Clean up temporary video file
        await _videoPersistenceService.cleanupTemporaryVideo(state.videoPath);

        // Show success message
        if (mounted) {
          scaffoldMessenger.showSnackBar(
            const SnackBar(
              content: Text('Video saved to gallery successfully!'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 2),
            ),
          );
        }

        // Navigate to video gallery to show the saved video
        if (mounted) {
          goRouter.go(AppRouter.videoGalleryPath);
        }
      } else {
        _logger.warning(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Video not saved - quality threshold not met or save failed',
          ),
        );

        // Navigate to video gallery anyway to show current state
        if (mounted) {
          goRouter.go(AppRouter.videoGalleryPath);
        }
      }
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.recoverableError,
          'Failed to handle successful verification: $error',
        ),
        error,
        stackTrace,
      );

      // Show error message but still navigate to gallery
      if (mounted) {
        scaffoldMessenger.showSnackBar(
          const SnackBar(
            content: Text('Video recorded but failed to save to gallery'),
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 3),
          ),
        );

        goRouter.go(AppRouter.videoGalleryPath);
      }
    }
  }

  /// Builds failure screen
  Widget _buildFailureScreen(BuildContext context, Failure state) {
    return ResultScreenWidget(
      isSuccess: false,
      title: 'Verification Failed',
      message: state.reason,
      coverageStats: state.coverageStats,
      onRetry: () {
        _safelyAccessBloc((bloc) => bloc.add(const ResetCapture()));
      },
    );
  }

  /// Builds error screen
  Widget _buildErrorScreen(BuildContext context, Error state) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 64,
            ),
            const SizedBox(height: 16),
            const Text(
              'Camera Error',
              style: TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              state.error,
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                _safelyAccessBloc((bloc) => bloc.add(const InitializeCamera()));
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }
}
