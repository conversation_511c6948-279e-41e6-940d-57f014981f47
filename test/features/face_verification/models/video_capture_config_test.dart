import 'package:bloomg_flutter/features/face_verification/models/video_capture_config.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('VideoCaptureConfig', () {
    test('should create with default values', () {
      const config = VideoCaptureConfig();

      expect(config.recordingDuration, const Duration(seconds: 9));
      expect(config.countdownDuration, const Duration(seconds: 3));
      expect(config.faceDetectionInterval, const Duration(milliseconds: 300));
      expect(config.minimumFaceCoverage, 80.0);
      expect(config.videoQuality, VideoQuality.high);
      expect(config.enableAudio, true);
      expect(config.cameraLens, CameraLens.front);
    });

    test('should create with custom values', () {
      const config = VideoCaptureConfig(
        recordingDuration: Duration(seconds: 15),
        countdownDuration: Duration(seconds: 5),
        faceDetectionInterval: Duration(milliseconds: 500),
        minimumFaceCoverage: 70,
        videoQuality: VideoQuality.medium,
        enableAudio: false,
        cameraLens: CameraLens.back,
      );

      expect(config.recordingDuration, const Duration(seconds: 15));
      expect(config.countdownDuration, const Duration(seconds: 5));
      expect(config.faceDetectionInterval, const Duration(milliseconds: 500));
      expect(config.minimumFaceCoverage, 70.0);
      expect(config.videoQuality, VideoQuality.medium);
      expect(config.enableAudio, false);
      expect(config.cameraLens, CameraLens.back);
    });

    test('should support equality comparison', () {
      const config1 = VideoCaptureConfig();
      const config2 = VideoCaptureConfig();
      const config3 =
          VideoCaptureConfig(recordingDuration: Duration(seconds: 15));

      expect(config1, equals(config2));
      expect(config1, isNot(equals(config3)));
    });

    test('should have proper props for equality', () {
      const config = VideoCaptureConfig();

      expect(config.props, [
        const Duration(seconds: 9),
        const Duration(seconds: 3),
        const Duration(milliseconds: 300),
        80.0,
        VideoQuality.high,
        true,
        CameraLens.front,
      ]);
    });

    group('VideoQuality enum', () {
      test('should have all expected values', () {
        expect(VideoQuality.values, [
          VideoQuality.low,
          VideoQuality.medium,
          VideoQuality.high,
        ]);
      });
    });

    group('CameraLens enum', () {
      test('should have all expected values', () {
        expect(CameraLens.values, [
          CameraLens.front,
          CameraLens.back,
        ]);
      });
    });

    test('should handle edge case values', () {
      const config = VideoCaptureConfig(
        recordingDuration: Duration.zero,
        countdownDuration: Duration.zero,
        faceDetectionInterval: Duration(milliseconds: 1),
        minimumFaceCoverage: 0,
      );

      expect(config.recordingDuration, Duration.zero);
      expect(config.countdownDuration, Duration.zero);
      expect(config.faceDetectionInterval, const Duration(milliseconds: 1));
      expect(config.minimumFaceCoverage, 0.0);
    });

    test('should handle maximum values', () {
      const config = VideoCaptureConfig(
        recordingDuration: Duration(seconds: 3600), // 1 hour
        countdownDuration: Duration(seconds: 60),
        faceDetectionInterval: Duration(seconds: 1),
        minimumFaceCoverage: 100,
      );

      expect(config.recordingDuration, const Duration(seconds: 3600));
      expect(config.countdownDuration, const Duration(seconds: 60));
      expect(config.faceDetectionInterval, const Duration(seconds: 1));
      expect(config.minimumFaceCoverage, 100.0);
    });
  });
}
